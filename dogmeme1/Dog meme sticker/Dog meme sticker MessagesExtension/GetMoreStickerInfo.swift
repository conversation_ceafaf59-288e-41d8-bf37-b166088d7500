//
//  GetMoreStickerInfo.swift
//  Cute duck animated 2 MessagesExtension
//
//  Created by le<PERSON><PERSON> on 10/6/24.
//

import Foundation

class PromoSticker: Identifiable {
    var id = UUID()
    var fileExtension: String
    var sticker1: String
    var sticker2: String
    var color: String
    var name: String
    var animate: Bool
    
    init(fileExtension: String, sticker1: String, sticker2: String, color: String, name: String, animate: Bool) {
        self.fileExtension = fileExtension
        self.sticker1 = sticker1
        self.sticker2 = sticker2
        self.color = color
        self.name = name
        self.animate = animate
    }
}

class OtherPromoSticker: PromoSticker {
    var downloadUrl: String
    
    init(fileExtension: String, sticker1: String, sticker2: String, color: String, downloadUrl: String, name: String, animate: Bool) {
        self.downloadUrl = downloadUrl
        super.init(fileExtension: fileExtension, sticker1: sticker1, sticker2: sticker2, color: color, name: name, animate: animate)
    }
}

class RatePromoSticker: PromoSticker {
    var url: String
    
    init(fileExtension: String, sticker1: String, sticker2: String, color: String, rateUrl: String, name: String, animate: Bool) {
        self.url = rateUrl
        super.init(fileExtension: fileExtension, sticker1: sticker1, sticker2: sticker2, color: color, name: name, animate: animate)
    }
}

let dogmeme1 = RatePromoSticker(fileExtension: "png", sticker1: "0", sticker2: "1", color: "#F7CB8A", rateUrl: "https://apps.apple.com/vn/app/dog-meme-sticker/id6504293229", name: "Dog meme", animate: false)

let duck1 = OtherPromoSticker(fileExtension: "gif", sticker1: "duck1", sticker2: "duck1_1", color: "#F6D051", downloadUrl: "https://apps.apple.com/vn/app/cute-duck-animated/id6479427936", name: "Duck animated", animate: true)

let pig1 = OtherPromoSticker(fileExtension: "png", sticker1: "pig", sticker2: "pig_1", color: "#FCC1C1", downloadUrl: "https://apps.apple.com/vn/app/cool-pig-animated/id6479258376", name: "Cute pig animated", animate: true)

let shiba1 = OtherPromoSticker(fileExtension: "gif", sticker1: "shiba", sticker2: "shiba_1", color: "#F7CB8A", downloadUrl: "https://apps.apple.com/vn/app/shiba-inu-sticker/id6475559907", name: "Shibaaa", animate: true)

let catmeme1 = OtherPromoSticker(fileExtension: "png", sticker1: "catmeme1", sticker2: "catmeme1_1", color: "#BBE1D4", downloadUrl: "https://apps.apple.com/vn/app/meme-cat-emotion/id6504249669", name: "Cat meme", animate: false)

let catmeme2 = OtherPromoSticker(fileExtension: "png", sticker1: "catmeme2", sticker2: "catmeme2_1", color: "#BBE1D4", downloadUrl: "https://apps.apple.com/vn/app/meme-cat-emotion-2/id6520391525", name: "Cat meme 2", animate: false)

// Creating a list of PromoSticker that includes both types
let promoStickers: [PromoSticker] = [duck1, pig1, shiba1, catmeme1, catmeme2, dogmeme1]

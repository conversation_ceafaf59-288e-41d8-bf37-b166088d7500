import Messages
import SwiftUI

let stickerSize = 90.0

struct ContentView: View {
    //need update===============================================================================
    @State private var stickers: [StickerItem] = []
    private let supportAnimation = false
    private let fileExtension = "png"
    //need update===============================================================================
    
    var openURLAction: (String) -> Void
    @State private var isShowingSheet = false
    private let adaptiveColumn = [
        GridItem(.adaptive(minimum: stickerSize))
    ]
    
    func loadStickers() {
        if let savedStickers = UserDefaults.standard.data(forKey: "stickers") {
            let decoder = JSONDecoder()
            if let loadedStickers = try? decoder.decode([StickerItem].self, from: savedStickers) {
                stickers = loadedStickers.sorted { ($0.lastUsed ?? Date.distantPast) > ($1.lastUsed ?? Date.distantPast) }
            }
        } else {
            // Initialize with default values if nothing is saved
            stickers = (0...41).map { StickerItem(id: $0) }
        }
    }
    
    func useSticker(_ sticker: StickerItem) {
        if let index = stickers.firstIndex(of: sticker) {
            stickers[index].lastUsed = Date()
            saveStickers()  // Save every time a sticker is used
        }
    }
    
    func saveStickers() {
        let encoder = JSONEncoder()
        if let encoded = try? encoder.encode(stickers) {
            UserDefaults.standard.set(encoded, forKey: "stickers")
        }
    }
    
    var body: some View {
        ScrollView {
            LazyVGrid(columns: adaptiveColumn, spacing: 10) {
                ForEach(stickers) { sticker in
                    Sticker(name: "\(sticker.id)", fileExtension: fileExtension, animate: supportAnimation)
                        .frame(width: stickerSize, height: stickerSize)
                        .onTapGesture {
                            useSticker(sticker)
                        }
                }
            }
            HStack {
                VStack {
                    Button("More") {
                        isShowingSheet.toggle()
                    }
                    .buttonStyle(.bordered)
                    .cornerRadius(20)
                    .bold()
                }
                Spacer()
                Sticker(name: promoStickers[0].sticker1, fileExtension: promoStickers[0].fileExtension, animate: promoStickers[0].animate)
                    .frame(width: 75, height: 75)
                Spacer()
                Sticker(name: promoStickers[1].sticker1, fileExtension: promoStickers[1].fileExtension, animate: promoStickers[1].animate)
                    .frame(width: 75, height: 75)
                Spacer()
                Sticker(name: promoStickers[2].sticker1, fileExtension: promoStickers[2].fileExtension, animate: promoStickers[2].animate)
                    .frame(width: 75, height: 75)
            }
            .padding(20)
            .background(
                Color.clear
            )
            .overlay(
                RoundedRectangle(cornerRadius: 30)
                    .stroke(Color(hex: "#FCC1C1"), lineWidth: 2)
            )
            .padding(EdgeInsets(top: 10, leading: 20, bottom: 10, trailing: 20))
        }
        .sheet(isPresented: $isShowingSheet) {
            OtherStickerSheet(openURLAction: openURLAction)
        }
        .onAppear {
            loadStickers()
        }
    }
}

struct OtherStickerSheet: View {
    var openURLAction: (String) -> Void
    @State private var isShowingShareSheet = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                Spacer().frame(height: 10)
                ForEach(promoStickers) {sticker in
                    if let otherSticker = sticker as? OtherPromoSticker {
                        HStack {
                            VStack {
                                Text("\(otherSticker.name)").bold()
                                Button("Download") {
                                    openURLAction("\(otherSticker.downloadUrl)")
                                }.buttonStyle(.bordered).cornerRadius(20).bold()
                            }
                            Spacer()
                            Sticker(name: otherSticker.sticker1, fileExtension: otherSticker.fileExtension, animate: otherSticker.animate).frame(width: 100, height: 100)
                            Sticker(name: otherSticker.sticker2, fileExtension: otherSticker.fileExtension, animate: otherSticker.animate).frame(width: 100, height: 100)
                        }.padding(20).background(Color(hex: otherSticker.color)).cornerRadius(30).padding(EdgeInsets(top: 10, leading: 20, bottom: 10, trailing: 20))
                    }  else if let rateSticker = sticker as? RatePromoSticker {
                        HStack {
                            VStack {
                                Button("Review") {
                                    openURLAction(rateSticker.url + "?action=write-review")
                                }
                                .buttonStyle(.bordered)
                                .cornerRadius(20)
                                .bold()
                                Button("Share") {
                                    isShowingShareSheet = true
                                }
                                .buttonStyle(.bordered)
                                .cornerRadius(20)
                                .bold()
                                .sheet(isPresented: $isShowingShareSheet) {
                                    ShareSheet(activityItems: [URL(string: rateSticker.url)!])
                                }
                            }
                            Spacer()
                            Spacer()
                            Sticker(name: rateSticker.sticker1, fileExtension: rateSticker.fileExtension, animate: rateSticker.animate).frame(width: 100, height: 100)
                            Sticker(name: rateSticker.sticker2, fileExtension: rateSticker.fileExtension, animate: rateSticker.animate).frame(width: 100, height: 100)
                        }
                        .padding(20)
                        .background(
                            Color.clear
                        )
                        .overlay(
                            RoundedRectangle(cornerRadius: 30)
                                .stroke(Color(hex: "#F8F39B"), lineWidth: 4)
                        )
                        .padding(EdgeInsets(top: 10, leading: 20, bottom: 10, trailing: 20))
                    }
                }
            }
        }
    }
    
    
}

extension Color {
    init(hex: String) {
        var hexSanitized = hex.trimmingCharacters(in: .whitespacesAndNewlines)
        hexSanitized = hexSanitized.replacingOccurrences(of: "#", with: "")
        
        var rgb: UInt64 = 0
        
        Scanner(string: hexSanitized).scanHexInt64(&rgb)
        
        let red = Double((rgb & 0xFF0000) >> 16) / 255.0
        let green = Double((rgb & 0x00FF00) >> 8) / 255.0
        let blue = Double(rgb & 0x0000FF) / 255.0
        
        self.init(red: red, green: green, blue: blue)
    }
}

@objc(MessagesViewController)
class MessagesViewController: MSMessagesAppViewController {
    override func viewDidLoad() {
        super.viewDidLoad()
        let child = UIHostingController(rootView: ContentView(openURLAction: self.openInMessagingURL))
        child.view.translatesAutoresizingMaskIntoConstraints = false
        self.view.addSubview(child.view)
        
        NSLayoutConstraint.activate([
            child.view.topAnchor.constraint(equalTo: view.topAnchor, constant: 0.0),
            child.view.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 0.0),
            child.view.bottomAnchor.constraint(equalTo: view.bottomAnchor, constant: 0.0),
            child.view.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: 0.0)
        ])
    }
    
    func openInMessagingURL(urlString: String) {
        guard let url = URL(string: urlString) else { return }
        let selectorOpenURL = NSSelectorFromString("openURL:")
        var responder: UIResponder? = self
        while let r = responder {
            if r.responds(to: selectorOpenURL), let application = r as? UIApplication {
                application.open(url, options: [:], completionHandler: nil)
                break
            }
            responder = r.next
        }
    }
}

struct ShareSheet: UIViewControllerRepresentable {
    var activityItems: [Any]
    var applicationActivities: [UIActivity]? = nil
    
    func makeUIViewController(context: Context) -> UIActivityViewController {
        let controller = UIActivityViewController(activityItems: activityItems, applicationActivities: applicationActivities)
        return controller
    }
    
    func updateUIViewController(_ uiViewController: UIActivityViewController, context: Context) {
        // No update needed
    }
}

struct StickerItem: Identifiable, Codable, Equatable {
    var id: Int
    var lastUsed: Date? = nil
}

struct Sticker: UIViewRepresentable {
    let name: String
    let fileExtension: String
    let animate: Bool
    
    func makeUIView(context: Context) -> MSStickerView {
        let sticker = loadStickerFile(name: name)!
        let stickerView = MSStickerView(frame: CGRect(x: 0, y: 0, width: stickerSize, height: stickerSize), sticker: sticker)
        stickerView.sizeToFit()
        if(animate) {
            stickerView.startAnimating()
        }
        return stickerView
    }
    
    func updateUIView(_ uiView: MSStickerView, context: Context) {
        if let sticker = loadStickerFile(name: name) {
            uiView.sticker = sticker
        }
    }
    
    func loadStickerFile(name: String) -> MSSticker? {
        guard let url = Bundle.main.url(forResource: "\(name)", withExtension: fileExtension) else {
            return nil
        }
        return try? MSSticker(contentsOfFileURL: url, localizedDescription: "\(name) sticker")
    }
}

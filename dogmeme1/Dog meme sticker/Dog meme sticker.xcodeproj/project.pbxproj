// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		49117D5A2C1B301F00E739A0 /* 23.png in Resources */ = {isa = PBXBuildFile; fileRef = 49117D302C1B301E00E739A0 /* 23.png */; };
		49117D5B2C1B301F00E739A0 /* 9.png in Resources */ = {isa = PBXBuildFile; fileRef = 49117D312C1B301E00E739A0 /* 9.png */; };
		49117D5C2C1B301F00E739A0 /* 14.png in Resources */ = {isa = PBXBuildFile; fileRef = 49117D322C1B301E00E739A0 /* 14.png */; };
		49117D5D2C1B301F00E739A0 /* 13.png in Resources */ = {isa = PBXBuildFile; fileRef = 49117D332C1B301E00E739A0 /* 13.png */; };
		49117D5E2C1B301F00E739A0 /* 12.png in Resources */ = {isa = PBXBuildFile; fileRef = 49117D342C1B301E00E739A0 /* 12.png */; };
		49117D5F2C1B301F00E739A0 /* 38.png in Resources */ = {isa = PBXBuildFile; fileRef = 49117D352C1B301E00E739A0 /* 38.png */; };
		49117D602C1B301F00E739A0 /* 8.png in Resources */ = {isa = PBXBuildFile; fileRef = 49117D362C1B301E00E739A0 /* 8.png */; };
		49117D612C1B301F00E739A0 /* 18.png in Resources */ = {isa = PBXBuildFile; fileRef = 49117D372C1B301E00E739A0 /* 18.png */; };
		49117D622C1B301F00E739A0 /* 30.png in Resources */ = {isa = PBXBuildFile; fileRef = 49117D382C1B301E00E739A0 /* 30.png */; };
		49117D632C1B301F00E739A0 /* 5.png in Resources */ = {isa = PBXBuildFile; fileRef = 49117D392C1B301E00E739A0 /* 5.png */; };
		49117D642C1B301F00E739A0 /* 24.png in Resources */ = {isa = PBXBuildFile; fileRef = 49117D3A2C1B301E00E739A0 /* 24.png */; };
		49117D652C1B301F00E739A0 /* 26.png in Resources */ = {isa = PBXBuildFile; fileRef = 49117D3B2C1B301E00E739A0 /* 26.png */; };
		49117D662C1B301F00E739A0 /* 33.png in Resources */ = {isa = PBXBuildFile; fileRef = 49117D3C2C1B301E00E739A0 /* 33.png */; };
		49117D672C1B301F00E739A0 /* 29.png in Resources */ = {isa = PBXBuildFile; fileRef = 49117D3D2C1B301E00E739A0 /* 29.png */; };
		49117D682C1B301F00E739A0 /* 2.png in Resources */ = {isa = PBXBuildFile; fileRef = 49117D3E2C1B301E00E739A0 /* 2.png */; };
		49117D692C1B301F00E739A0 /* 37.png in Resources */ = {isa = PBXBuildFile; fileRef = 49117D3F2C1B301E00E739A0 /* 37.png */; };
		49117D6A2C1B301F00E739A0 /* 11.png in Resources */ = {isa = PBXBuildFile; fileRef = 49117D402C1B301E00E739A0 /* 11.png */; };
		49117D6B2C1B301F00E739A0 /* 25.png in Resources */ = {isa = PBXBuildFile; fileRef = 49117D412C1B301E00E739A0 /* 25.png */; };
		49117D6C2C1B301F00E739A0 /* 16.png in Resources */ = {isa = PBXBuildFile; fileRef = 49117D422C1B301E00E739A0 /* 16.png */; };
		49117D6D2C1B301F00E739A0 /* 3.png in Resources */ = {isa = PBXBuildFile; fileRef = 49117D432C1B301E00E739A0 /* 3.png */; };
		49117D6E2C1B301F00E739A0 /* 4.png in Resources */ = {isa = PBXBuildFile; fileRef = 49117D442C1B301E00E739A0 /* 4.png */; };
		49117D6F2C1B301F00E739A0 /* 21.png in Resources */ = {isa = PBXBuildFile; fileRef = 49117D452C1B301E00E739A0 /* 21.png */; };
		49117D702C1B301F00E739A0 /* 15.png in Resources */ = {isa = PBXBuildFile; fileRef = 49117D462C1B301E00E739A0 /* 15.png */; };
		49117D712C1B301F00E739A0 /* 22.png in Resources */ = {isa = PBXBuildFile; fileRef = 49117D472C1B301E00E739A0 /* 22.png */; };
		49117D722C1B301F00E739A0 /* 27.png in Resources */ = {isa = PBXBuildFile; fileRef = 49117D482C1B301E00E739A0 /* 27.png */; };
		49117D732C1B301F00E739A0 /* 17.png in Resources */ = {isa = PBXBuildFile; fileRef = 49117D492C1B301E00E739A0 /* 17.png */; };
		49117D742C1B301F00E739A0 /* 31.png in Resources */ = {isa = PBXBuildFile; fileRef = 49117D4A2C1B301E00E739A0 /* 31.png */; };
		49117D752C1B301F00E739A0 /* 1.png in Resources */ = {isa = PBXBuildFile; fileRef = 49117D4B2C1B301E00E739A0 /* 1.png */; };
		49117D762C1B301F00E739A0 /* 39.png in Resources */ = {isa = PBXBuildFile; fileRef = 49117D4C2C1B301F00E739A0 /* 39.png */; };
		49117D772C1B301F00E739A0 /* 7.png in Resources */ = {isa = PBXBuildFile; fileRef = 49117D4D2C1B301F00E739A0 /* 7.png */; };
		49117D782C1B301F00E739A0 /* 10.png in Resources */ = {isa = PBXBuildFile; fileRef = 49117D4E2C1B301F00E739A0 /* 10.png */; };
		49117D792C1B301F00E739A0 /* 28.png in Resources */ = {isa = PBXBuildFile; fileRef = 49117D4F2C1B301F00E739A0 /* 28.png */; };
		49117D7A2C1B301F00E739A0 /* 32.png in Resources */ = {isa = PBXBuildFile; fileRef = 49117D502C1B301F00E739A0 /* 32.png */; };
		49117D7B2C1B301F00E739A0 /* 34.png in Resources */ = {isa = PBXBuildFile; fileRef = 49117D512C1B301F00E739A0 /* 34.png */; };
		49117D7C2C1B301F00E739A0 /* 35.png in Resources */ = {isa = PBXBuildFile; fileRef = 49117D522C1B301F00E739A0 /* 35.png */; };
		49117D7D2C1B301F00E739A0 /* 36.png in Resources */ = {isa = PBXBuildFile; fileRef = 49117D532C1B301F00E739A0 /* 36.png */; };
		49117D7E2C1B301F00E739A0 /* 6.png in Resources */ = {isa = PBXBuildFile; fileRef = 49117D542C1B301F00E739A0 /* 6.png */; };
		49117D7F2C1B301F00E739A0 /* 20.png in Resources */ = {isa = PBXBuildFile; fileRef = 49117D552C1B301F00E739A0 /* 20.png */; };
		49117D802C1B301F00E739A0 /* 0.png in Resources */ = {isa = PBXBuildFile; fileRef = 49117D562C1B301F00E739A0 /* 0.png */; };
		49117D812C1B301F00E739A0 /* 40.png in Resources */ = {isa = PBXBuildFile; fileRef = 49117D572C1B301F00E739A0 /* 40.png */; };
		49117D822C1B301F00E739A0 /* 41.png in Resources */ = {isa = PBXBuildFile; fileRef = 49117D582C1B301F00E739A0 /* 41.png */; };
		49117D832C1B301F00E739A0 /* 19.png in Resources */ = {isa = PBXBuildFile; fileRef = 49117D592C1B301F00E739A0 /* 19.png */; };
		497123252C66EDCB0084D0A1 /* catmeme2_1.png in Resources */ = {isa = PBXBuildFile; fileRef = 497123232C66EDCB0084D0A1 /* catmeme2_1.png */; };
		497123262C66EDCB0084D0A1 /* catmeme2.png in Resources */ = {isa = PBXBuildFile; fileRef = 497123242C66EDCB0084D0A1 /* catmeme2.png */; };
		499F84872C19D92D00E39C8B /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 499F84862C19D92D00E39C8B /* Assets.xcassets */; };
		499F848D2C19D92E00E39C8B /* Dog meme sticker MessagesExtension.appex in Embed Foundation Extensions */ = {isa = PBXBuildFile; fileRef = 499F848C2C19D92E00E39C8B /* Dog meme sticker MessagesExtension.appex */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
		499F84922C19D92E00E39C8B /* Messages.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 499F84912C19D92E00E39C8B /* Messages.framework */; };
		499F84952C19D92E00E39C8B /* MessagesViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 499F84942C19D92E00E39C8B /* MessagesViewController.swift */; };
		499F849A2C19D92F00E39C8B /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 499F84992C19D92F00E39C8B /* Assets.xcassets */; };
		499F84FF2C19DB8F00E39C8B /* GetMoreStickerInfo.swift in Sources */ = {isa = PBXBuildFile; fileRef = 499F84FE2C19DB8F00E39C8B /* GetMoreStickerInfo.swift */; };
		499F85092C19F15700E39C8B /* catmeme1.png in Resources */ = {isa = PBXBuildFile; fileRef = 499F85012C19F15700E39C8B /* catmeme1.png */; };
		499F850A2C19F15700E39C8B /* shiba.gif in Resources */ = {isa = PBXBuildFile; fileRef = 499F85022C19F15700E39C8B /* shiba.gif */; };
		499F850B2C19F15700E39C8B /* pig.png in Resources */ = {isa = PBXBuildFile; fileRef = 499F85032C19F15700E39C8B /* pig.png */; };
		499F850C2C19F15700E39C8B /* duck1_1.gif in Resources */ = {isa = PBXBuildFile; fileRef = 499F85042C19F15700E39C8B /* duck1_1.gif */; };
		499F850D2C19F15700E39C8B /* pig_1.png in Resources */ = {isa = PBXBuildFile; fileRef = 499F85052C19F15700E39C8B /* pig_1.png */; };
		499F850E2C19F15700E39C8B /* shiba_1.gif in Resources */ = {isa = PBXBuildFile; fileRef = 499F85062C19F15700E39C8B /* shiba_1.gif */; };
		499F850F2C19F15700E39C8B /* catmeme1_1.png in Resources */ = {isa = PBXBuildFile; fileRef = 499F85072C19F15700E39C8B /* catmeme1_1.png */; };
		499F85102C19F15700E39C8B /* duck1.gif in Resources */ = {isa = PBXBuildFile; fileRef = 499F85082C19F15700E39C8B /* duck1.gif */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		499F848E2C19D92E00E39C8B /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 499F847D2C19D92B00E39C8B /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 499F848B2C19D92E00E39C8B;
			remoteInfo = "Dog meme sticker MessagesExtension";
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		499F84A12C19D92F00E39C8B /* Embed Foundation Extensions */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 13;
			files = (
				499F848D2C19D92E00E39C8B /* Dog meme sticker MessagesExtension.appex in Embed Foundation Extensions */,
			);
			name = "Embed Foundation Extensions";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		49117D302C1B301E00E739A0 /* 23.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 23.png; sourceTree = "<group>"; };
		49117D312C1B301E00E739A0 /* 9.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 9.png; sourceTree = "<group>"; };
		49117D322C1B301E00E739A0 /* 14.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 14.png; sourceTree = "<group>"; };
		49117D332C1B301E00E739A0 /* 13.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 13.png; sourceTree = "<group>"; };
		49117D342C1B301E00E739A0 /* 12.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 12.png; sourceTree = "<group>"; };
		49117D352C1B301E00E739A0 /* 38.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 38.png; sourceTree = "<group>"; };
		49117D362C1B301E00E739A0 /* 8.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 8.png; sourceTree = "<group>"; };
		49117D372C1B301E00E739A0 /* 18.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 18.png; sourceTree = "<group>"; };
		49117D382C1B301E00E739A0 /* 30.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 30.png; sourceTree = "<group>"; };
		49117D392C1B301E00E739A0 /* 5.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 5.png; sourceTree = "<group>"; };
		49117D3A2C1B301E00E739A0 /* 24.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 24.png; sourceTree = "<group>"; };
		49117D3B2C1B301E00E739A0 /* 26.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 26.png; sourceTree = "<group>"; };
		49117D3C2C1B301E00E739A0 /* 33.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 33.png; sourceTree = "<group>"; };
		49117D3D2C1B301E00E739A0 /* 29.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 29.png; sourceTree = "<group>"; };
		49117D3E2C1B301E00E739A0 /* 2.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 2.png; sourceTree = "<group>"; };
		49117D3F2C1B301E00E739A0 /* 37.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 37.png; sourceTree = "<group>"; };
		49117D402C1B301E00E739A0 /* 11.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 11.png; sourceTree = "<group>"; };
		49117D412C1B301E00E739A0 /* 25.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 25.png; sourceTree = "<group>"; };
		49117D422C1B301E00E739A0 /* 16.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 16.png; sourceTree = "<group>"; };
		49117D432C1B301E00E739A0 /* 3.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 3.png; sourceTree = "<group>"; };
		49117D442C1B301E00E739A0 /* 4.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 4.png; sourceTree = "<group>"; };
		49117D452C1B301E00E739A0 /* 21.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 21.png; sourceTree = "<group>"; };
		49117D462C1B301E00E739A0 /* 15.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 15.png; sourceTree = "<group>"; };
		49117D472C1B301E00E739A0 /* 22.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 22.png; sourceTree = "<group>"; };
		49117D482C1B301E00E739A0 /* 27.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 27.png; sourceTree = "<group>"; };
		49117D492C1B301E00E739A0 /* 17.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 17.png; sourceTree = "<group>"; };
		49117D4A2C1B301E00E739A0 /* 31.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 31.png; sourceTree = "<group>"; };
		49117D4B2C1B301E00E739A0 /* 1.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 1.png; sourceTree = "<group>"; };
		49117D4C2C1B301F00E739A0 /* 39.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 39.png; sourceTree = "<group>"; };
		49117D4D2C1B301F00E739A0 /* 7.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 7.png; sourceTree = "<group>"; };
		49117D4E2C1B301F00E739A0 /* 10.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 10.png; sourceTree = "<group>"; };
		49117D4F2C1B301F00E739A0 /* 28.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 28.png; sourceTree = "<group>"; };
		49117D502C1B301F00E739A0 /* 32.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 32.png; sourceTree = "<group>"; };
		49117D512C1B301F00E739A0 /* 34.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 34.png; sourceTree = "<group>"; };
		49117D522C1B301F00E739A0 /* 35.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 35.png; sourceTree = "<group>"; };
		49117D532C1B301F00E739A0 /* 36.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 36.png; sourceTree = "<group>"; };
		49117D542C1B301F00E739A0 /* 6.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 6.png; sourceTree = "<group>"; };
		49117D552C1B301F00E739A0 /* 20.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 20.png; sourceTree = "<group>"; };
		49117D562C1B301F00E739A0 /* 0.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 0.png; sourceTree = "<group>"; };
		49117D572C1B301F00E739A0 /* 40.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 40.png; sourceTree = "<group>"; };
		49117D582C1B301F00E739A0 /* 41.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 41.png; sourceTree = "<group>"; };
		49117D592C1B301F00E739A0 /* 19.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 19.png; sourceTree = "<group>"; };
		4965EC082DE149FA006B4567 /* Dog meme sticker MessagesExtension.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = "Dog meme sticker MessagesExtension.entitlements"; sourceTree = "<group>"; };
		497123232C66EDCB0084D0A1 /* catmeme2_1.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = catmeme2_1.png; sourceTree = "<group>"; };
		497123242C66EDCB0084D0A1 /* catmeme2.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = catmeme2.png; sourceTree = "<group>"; };
		499F84832C19D92B00E39C8B /* Dog meme sticker.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "Dog meme sticker.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		499F84862C19D92D00E39C8B /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		499F848C2C19D92E00E39C8B /* Dog meme sticker MessagesExtension.appex */ = {isa = PBXFileReference; explicitFileType = "wrapper.app-extension"; includeInIndex = 0; path = "Dog meme sticker MessagesExtension.appex"; sourceTree = BUILT_PRODUCTS_DIR; };
		499F84912C19D92E00E39C8B /* Messages.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Messages.framework; path = System/Library/Frameworks/Messages.framework; sourceTree = SDKROOT; };
		499F84942C19D92E00E39C8B /* MessagesViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MessagesViewController.swift; sourceTree = "<group>"; };
		499F84992C19D92F00E39C8B /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		499F849B2C19D92F00E39C8B /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		499F84FE2C19DB8F00E39C8B /* GetMoreStickerInfo.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GetMoreStickerInfo.swift; sourceTree = "<group>"; };
		499F85012C19F15700E39C8B /* catmeme1.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = catmeme1.png; sourceTree = "<group>"; };
		499F85022C19F15700E39C8B /* shiba.gif */ = {isa = PBXFileReference; lastKnownFileType = image.gif; path = shiba.gif; sourceTree = "<group>"; };
		499F85032C19F15700E39C8B /* pig.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = pig.png; sourceTree = "<group>"; };
		499F85042C19F15700E39C8B /* duck1_1.gif */ = {isa = PBXFileReference; lastKnownFileType = image.gif; path = duck1_1.gif; sourceTree = "<group>"; };
		499F85052C19F15700E39C8B /* pig_1.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = pig_1.png; sourceTree = "<group>"; };
		499F85062C19F15700E39C8B /* shiba_1.gif */ = {isa = PBXFileReference; lastKnownFileType = image.gif; path = shiba_1.gif; sourceTree = "<group>"; };
		499F85072C19F15700E39C8B /* catmeme1_1.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = catmeme1_1.png; sourceTree = "<group>"; };
		499F85082C19F15700E39C8B /* duck1.gif */ = {isa = PBXFileReference; lastKnownFileType = image.gif; path = duck1.gif; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		499F84892C19D92E00E39C8B /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				499F84922C19D92E00E39C8B /* Messages.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		499F847C2C19D92B00E39C8B = {
			isa = PBXGroup;
			children = (
				499F84852C19D92B00E39C8B /* Dog meme sticker */,
				499F84932C19D92E00E39C8B /* Dog meme sticker MessagesExtension */,
				499F84902C19D92E00E39C8B /* Frameworks */,
				499F84842C19D92B00E39C8B /* Products */,
			);
			sourceTree = "<group>";
		};
		499F84842C19D92B00E39C8B /* Products */ = {
			isa = PBXGroup;
			children = (
				499F84832C19D92B00E39C8B /* Dog meme sticker.app */,
				499F848C2C19D92E00E39C8B /* Dog meme sticker MessagesExtension.appex */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		499F84852C19D92B00E39C8B /* Dog meme sticker */ = {
			isa = PBXGroup;
			children = (
				499F84862C19D92D00E39C8B /* Assets.xcassets */,
			);
			path = "Dog meme sticker";
			sourceTree = "<group>";
		};
		499F84902C19D92E00E39C8B /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				499F84912C19D92E00E39C8B /* Messages.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		499F84932C19D92E00E39C8B /* Dog meme sticker MessagesExtension */ = {
			isa = PBXGroup;
			children = (
				4965EC082DE149FA006B4567 /* Dog meme sticker MessagesExtension.entitlements */,
				499F85002C19F10D00E39C8B /* others */,
				499F84A52C19DAAF00E39C8B /* stickers */,
				499F84942C19D92E00E39C8B /* MessagesViewController.swift */,
				499F84992C19D92F00E39C8B /* Assets.xcassets */,
				499F849B2C19D92F00E39C8B /* Info.plist */,
				499F84FE2C19DB8F00E39C8B /* GetMoreStickerInfo.swift */,
			);
			path = "Dog meme sticker MessagesExtension";
			sourceTree = "<group>";
		};
		499F84A52C19DAAF00E39C8B /* stickers */ = {
			isa = PBXGroup;
			children = (
				49117D562C1B301F00E739A0 /* 0.png */,
				49117D4B2C1B301E00E739A0 /* 1.png */,
				49117D3E2C1B301E00E739A0 /* 2.png */,
				49117D432C1B301E00E739A0 /* 3.png */,
				49117D442C1B301E00E739A0 /* 4.png */,
				49117D392C1B301E00E739A0 /* 5.png */,
				49117D542C1B301F00E739A0 /* 6.png */,
				49117D4D2C1B301F00E739A0 /* 7.png */,
				49117D362C1B301E00E739A0 /* 8.png */,
				49117D312C1B301E00E739A0 /* 9.png */,
				49117D4E2C1B301F00E739A0 /* 10.png */,
				49117D402C1B301E00E739A0 /* 11.png */,
				49117D342C1B301E00E739A0 /* 12.png */,
				49117D332C1B301E00E739A0 /* 13.png */,
				49117D322C1B301E00E739A0 /* 14.png */,
				49117D462C1B301E00E739A0 /* 15.png */,
				49117D422C1B301E00E739A0 /* 16.png */,
				49117D492C1B301E00E739A0 /* 17.png */,
				49117D372C1B301E00E739A0 /* 18.png */,
				49117D592C1B301F00E739A0 /* 19.png */,
				49117D552C1B301F00E739A0 /* 20.png */,
				49117D452C1B301E00E739A0 /* 21.png */,
				49117D472C1B301E00E739A0 /* 22.png */,
				49117D302C1B301E00E739A0 /* 23.png */,
				49117D3A2C1B301E00E739A0 /* 24.png */,
				49117D412C1B301E00E739A0 /* 25.png */,
				49117D3B2C1B301E00E739A0 /* 26.png */,
				49117D482C1B301E00E739A0 /* 27.png */,
				49117D4F2C1B301F00E739A0 /* 28.png */,
				49117D3D2C1B301E00E739A0 /* 29.png */,
				49117D382C1B301E00E739A0 /* 30.png */,
				49117D4A2C1B301E00E739A0 /* 31.png */,
				49117D502C1B301F00E739A0 /* 32.png */,
				49117D3C2C1B301E00E739A0 /* 33.png */,
				49117D512C1B301F00E739A0 /* 34.png */,
				49117D522C1B301F00E739A0 /* 35.png */,
				49117D532C1B301F00E739A0 /* 36.png */,
				49117D3F2C1B301E00E739A0 /* 37.png */,
				49117D352C1B301E00E739A0 /* 38.png */,
				49117D4C2C1B301F00E739A0 /* 39.png */,
				49117D572C1B301F00E739A0 /* 40.png */,
				49117D582C1B301F00E739A0 /* 41.png */,
			);
			path = stickers;
			sourceTree = "<group>";
		};
		499F85002C19F10D00E39C8B /* others */ = {
			isa = PBXGroup;
			children = (
				497123232C66EDCB0084D0A1 /* catmeme2_1.png */,
				497123242C66EDCB0084D0A1 /* catmeme2.png */,
				499F85072C19F15700E39C8B /* catmeme1_1.png */,
				499F85012C19F15700E39C8B /* catmeme1.png */,
				499F85042C19F15700E39C8B /* duck1_1.gif */,
				499F85082C19F15700E39C8B /* duck1.gif */,
				499F85052C19F15700E39C8B /* pig_1.png */,
				499F85032C19F15700E39C8B /* pig.png */,
				499F85062C19F15700E39C8B /* shiba_1.gif */,
				499F85022C19F15700E39C8B /* shiba.gif */,
			);
			path = others;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		499F84822C19D92B00E39C8B /* Dog meme sticker */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 499F84A22C19D92F00E39C8B /* Build configuration list for PBXNativeTarget "Dog meme sticker" */;
			buildPhases = (
				499F84812C19D92B00E39C8B /* Resources */,
				499F84A12C19D92F00E39C8B /* Embed Foundation Extensions */,
			);
			buildRules = (
			);
			dependencies = (
				499F848F2C19D92E00E39C8B /* PBXTargetDependency */,
			);
			name = "Dog meme sticker";
			productName = "Dog meme sticker";
			productReference = 499F84832C19D92B00E39C8B /* Dog meme sticker.app */;
			productType = "com.apple.product-type.application.messages";
		};
		499F848B2C19D92E00E39C8B /* Dog meme sticker MessagesExtension */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 499F849E2C19D92F00E39C8B /* Build configuration list for PBXNativeTarget "Dog meme sticker MessagesExtension" */;
			buildPhases = (
				499F84882C19D92E00E39C8B /* Sources */,
				499F84892C19D92E00E39C8B /* Frameworks */,
				499F848A2C19D92E00E39C8B /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "Dog meme sticker MessagesExtension";
			productName = "Dog meme sticker MessagesExtension";
			productReference = 499F848C2C19D92E00E39C8B /* Dog meme sticker MessagesExtension.appex */;
			productType = "com.apple.product-type.app-extension.messages";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		499F847D2C19D92B00E39C8B /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1520;
				LastUpgradeCheck = 1520;
				TargetAttributes = {
					499F84822C19D92B00E39C8B = {
						CreatedOnToolsVersion = 15.2;
					};
					499F848B2C19D92E00E39C8B = {
						CreatedOnToolsVersion = 15.2;
					};
				};
			};
			buildConfigurationList = 499F84802C19D92B00E39C8B /* Build configuration list for PBXProject "Dog meme sticker" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 499F847C2C19D92B00E39C8B;
			productRefGroup = 499F84842C19D92B00E39C8B /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				499F84822C19D92B00E39C8B /* Dog meme sticker */,
				499F848B2C19D92E00E39C8B /* Dog meme sticker MessagesExtension */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		499F84812C19D92B00E39C8B /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				499F84872C19D92D00E39C8B /* Assets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		499F848A2C19D92E00E39C8B /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				49117D602C1B301F00E739A0 /* 8.png in Resources */,
				49117D5D2C1B301F00E739A0 /* 13.png in Resources */,
				49117D672C1B301F00E739A0 /* 29.png in Resources */,
				49117D762C1B301F00E739A0 /* 39.png in Resources */,
				49117D632C1B301F00E739A0 /* 5.png in Resources */,
				49117D6C2C1B301F00E739A0 /* 16.png in Resources */,
				49117D662C1B301F00E739A0 /* 33.png in Resources */,
				49117D652C1B301F00E739A0 /* 26.png in Resources */,
				49117D712C1B301F00E739A0 /* 22.png in Resources */,
				49117D5B2C1B301F00E739A0 /* 9.png in Resources */,
				49117D782C1B301F00E739A0 /* 10.png in Resources */,
				49117D6F2C1B301F00E739A0 /* 21.png in Resources */,
				49117D5E2C1B301F00E739A0 /* 12.png in Resources */,
				49117D622C1B301F00E739A0 /* 30.png in Resources */,
				49117D832C1B301F00E739A0 /* 19.png in Resources */,
				49117D7F2C1B301F00E739A0 /* 20.png in Resources */,
				49117D7A2C1B301F00E739A0 /* 32.png in Resources */,
				49117D5A2C1B301F00E739A0 /* 23.png in Resources */,
				49117D6D2C1B301F00E739A0 /* 3.png in Resources */,
				49117D722C1B301F00E739A0 /* 27.png in Resources */,
				49117D812C1B301F00E739A0 /* 40.png in Resources */,
				49117D732C1B301F00E739A0 /* 17.png in Resources */,
				499F85092C19F15700E39C8B /* catmeme1.png in Resources */,
				49117D5F2C1B301F00E739A0 /* 38.png in Resources */,
				499F850F2C19F15700E39C8B /* catmeme1_1.png in Resources */,
				49117D6B2C1B301F00E739A0 /* 25.png in Resources */,
				49117D802C1B301F00E739A0 /* 0.png in Resources */,
				49117D6A2C1B301F00E739A0 /* 11.png in Resources */,
				49117D742C1B301F00E739A0 /* 31.png in Resources */,
				49117D7D2C1B301F00E739A0 /* 36.png in Resources */,
				499F85102C19F15700E39C8B /* duck1.gif in Resources */,
				49117D822C1B301F00E739A0 /* 41.png in Resources */,
				49117D6E2C1B301F00E739A0 /* 4.png in Resources */,
				499F850E2C19F15700E39C8B /* shiba_1.gif in Resources */,
				49117D7B2C1B301F00E739A0 /* 34.png in Resources */,
				49117D692C1B301F00E739A0 /* 37.png in Resources */,
				497123262C66EDCB0084D0A1 /* catmeme2.png in Resources */,
				49117D772C1B301F00E739A0 /* 7.png in Resources */,
				49117D7E2C1B301F00E739A0 /* 6.png in Resources */,
				499F850A2C19F15700E39C8B /* shiba.gif in Resources */,
				49117D612C1B301F00E739A0 /* 18.png in Resources */,
				499F850C2C19F15700E39C8B /* duck1_1.gif in Resources */,
				49117D682C1B301F00E739A0 /* 2.png in Resources */,
				49117D792C1B301F00E739A0 /* 28.png in Resources */,
				49117D642C1B301F00E739A0 /* 24.png in Resources */,
				499F850B2C19F15700E39C8B /* pig.png in Resources */,
				499F849A2C19D92F00E39C8B /* Assets.xcassets in Resources */,
				49117D5C2C1B301F00E739A0 /* 14.png in Resources */,
				497123252C66EDCB0084D0A1 /* catmeme2_1.png in Resources */,
				499F850D2C19F15700E39C8B /* pig_1.png in Resources */,
				49117D7C2C1B301F00E739A0 /* 35.png in Resources */,
				49117D702C1B301F00E739A0 /* 15.png in Resources */,
				49117D752C1B301F00E739A0 /* 1.png in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		499F84882C19D92E00E39C8B /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				499F84952C19D92E00E39C8B /* MessagesViewController.swift in Sources */,
				499F84FF2C19DB8F00E39C8B /* GetMoreStickerInfo.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		499F848F2C19D92E00E39C8B /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 499F848B2C19D92E00E39C8B /* Dog meme sticker MessagesExtension */;
			targetProxy = 499F848E2C19D92E00E39C8B /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		499F849C2C19D92F00E39C8B /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		499F849D2C19D92F00E39C8B /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		499F849F2C19D92F00E39C8B /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = "iMessage App Icon";
				CODE_SIGN_ENTITLEMENTS = "Dog meme sticker MessagesExtension/Dog meme sticker MessagesExtension.entitlements";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1.2;
				DEVELOPMENT_TEAM = T2ZYL8JN5D;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = "Dog meme sticker MessagesExtension/Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = "Dog meme sticker";
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 1.2;
				PRODUCT_BUNDLE_IDENTIFIER = "leongovan.Dog-meme-sticker.MessagesExtension";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		499F84A02C19D92F00E39C8B /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = "iMessage App Icon";
				CODE_SIGN_ENTITLEMENTS = "Dog meme sticker MessagesExtension/Dog meme sticker MessagesExtension.entitlements";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1.2;
				DEVELOPMENT_TEAM = T2ZYL8JN5D;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = "Dog meme sticker MessagesExtension/Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = "Dog meme sticker";
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 1.2;
				PRODUCT_BUNDLE_IDENTIFIER = "leongovan.Dog-meme-sticker.MessagesExtension";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		499F84A32C19D92F00E39C8B /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1.2;
				DEVELOPMENT_TEAM = T2ZYL8JN5D;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				MARKETING_VERSION = 1.2;
				PRODUCT_BUNDLE_IDENTIFIER = "leongovan.Dog-meme-sticker";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		499F84A42C19D92F00E39C8B /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1.2;
				DEVELOPMENT_TEAM = T2ZYL8JN5D;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				MARKETING_VERSION = 1.2;
				PRODUCT_BUNDLE_IDENTIFIER = "leongovan.Dog-meme-sticker";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		499F84802C19D92B00E39C8B /* Build configuration list for PBXProject "Dog meme sticker" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				499F849C2C19D92F00E39C8B /* Debug */,
				499F849D2C19D92F00E39C8B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		499F849E2C19D92F00E39C8B /* Build configuration list for PBXNativeTarget "Dog meme sticker MessagesExtension" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				499F849F2C19D92F00E39C8B /* Debug */,
				499F84A02C19D92F00E39C8B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		499F84A22C19D92F00E39C8B /* Build configuration list for PBXNativeTarget "Dog meme sticker" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				499F84A32C19D92F00E39C8B /* Debug */,
				499F84A42C19D92F00E39C8B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 499F847D2C19D92B00E39C8B /* Project object */;
}
